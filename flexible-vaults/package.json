{"name": "flexible-vaults", "license": "UNLICENSED", "version": "1.0.0", "repository": "https://github.com/mellow-finance/flexible-vaults", "scripts": {"prettier": "forge fmt ./src/* ./test/* ./scripts/*", "test": "forge test --fork-url $(grep ETH_RPC .env | cut -d '=' -f2,3,4,5) --gas-limit 10000000000000000 --fork-block-number 22730425 -vvv", "test:integration": "forge test --fork-url $(grep ETH_RPC .env | cut -d '=' -f2,3,4,5) --gas-limit 10000000000000000 --fork-block-number 22730425 -vvv --match-path './test/integration/**'", "coverage:report": "forge coverage --fork-url $(grep ETH_RPC .env | cut -d '=' -f2,3,4,5) --gas-limit 10000000000000000 --fork-block-number 22730425 --force --report lcov && genhtml lcov.info -o report --branch-coverage && rm lcov.info", "compile": "forge build --use 0.8.25 --cache-path cache", "sizes": "forge build --sizes --force", "scc:report": "scc src --sort names --no-cocomo --exclude-dir interfaces --by-file --format wide > scc-report.txt"}}